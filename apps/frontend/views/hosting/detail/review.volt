<div class="col l-12 mc-12 c-12">
  <div class="customer-reviews">
    <div class="customer-reviews__container">
      <div class="customer-reviews__card">
        <div class="customer-reviews__header box-home__title">
          <h2><PERSON><PERSON><PERSON> g<PERSON> từ khách hàng</h2>
        </div>

        <div class="customer-reviews__content">
          <div class="rating-overview">
            <div class="rating-overview__main">
              <div class="rating-overview__score">
                {{ number_format(hosting.rating.overall_avg ? hosting.rating.overall_avg : 0) }}
              </div>
              <div class="rating-overview__details">
                <div class="rating-stars">
                  {% set fullStars = hosting.rating.overall_avg ? floor(hosting.rating.overall_avg) : 0 %}
                  {% set halfStar = (hosting.rating.overall_avg and (hosting.rating.overall_avg - fullStars) >= 0.25 and (hosting.rating.overall_avg - fullStars) < 0.75) ? 1 : 0 %}
                  {% set emptyStars = 5 - fullStars - halfStar %}
                  {% for i in 1..fullStars %}
                    <svg class="rating-stars__star rating-stars__star--filled" viewBox="0 0 24 24">
                      <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                    </svg>
                  {% endfor %}
                  {% if halfStar %}
                    <svg class="rating-stars__star rating-stars__star--partial" viewBox="0 0 24 24">
                      <defs>
                        <linearGradient id="half-grad">
                          <stop offset="50%" stop-color="#FFD700"/>
                          <stop offset="50%" stop-color="#E0E0E0"/>
                        </linearGradient>
                      </defs>
                      <polygon fill="url(#half-grad)" points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                    </svg>
                  {% endif %}
                  {% for i in 1..emptyStars %}
                    <svg class="rating-stars__star rating-stars__star--empty" viewBox="0 0 24 24">
                      <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                    </svg>
                  {% endfor %}
                </div>
                <p class="rating-overview__count">
                  Dựa trên {{ hosting.rating.total_reviews ? hosting.rating.total_reviews : 0 }} đánh giá
                </p>
              </div>
            </div>
            <div class="rating-overview__summary">
              <p class="rating-overview__label">
                {{ hosting.rating.overall_avg >= 4.5 ? 'Xuất sắc' : (hosting.rating.overall_avg >= 3.5 ? 'Tốt' : (hosting.rating.overall_avg > 0 ? 'Trung bình' : 'Chưa có đánh giá')) }}
              </p>
              <p class="rating-overview__description">Đánh giá tổng thể</p>
            </div>
          </div>

          <div class="customer-reviews__separator"></div>

          <!-- Metrics Section -->
          <div class="rating-breakdown">
            <h3 class="rating-breakdown__title">Chi tiết đánh giá</h3>
            <div class="rating-breakdown__list">
              {% set metrics = [
                ['Sạch sẽ', 'cleanliness_avg'],
                ['Dịch vụ', 'service_avg'],
                ['Giá trị', 'value_avg'],
                ['Vị trí', 'location_avg'],
                ['Tiện nghi', 'facilities_avg']
              ] %}
              {% for metric in metrics %}
                <div class="rating-metric">
                  <div class="rating-metric__label">{{ metric[0] }}</div>
                  <div class="rating-metric__progress">
                    <div class="rating-metric__bar">
                      <div class="rating-metric__fill" style="width: {{ Ohi.calcPercent(readAttribute(hosting.rating, metric[1]), 5) }}%"></div>
                    </div>
                  </div>
                  <div class="rating-metric__score">
                    <svg class="rating-metric__star" viewBox="0 0 24 24">
                      <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                    </svg>
                    <span class="rating-metric__value">
                      {{ number_format(readAttribute(hosting.rating, metric[1]) ? readAttribute(hosting.rating, metric[1]) : 0) }}
                    </span>
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>

          <div class="customer-reviews__separator"></div>

          <!-- Individual Reviews Section -->
          {# {% if hosting.rating.reviews is defined and hosting.rating.reviews|length > 0 %}
          <div class="reviews-section">
            <h3 class="reviews-section__title">Đánh giá gần đây</h3>
            <div class="reviews-list">
              {% for review in hosting.rating.reviews %}
                <div class="review-item">
                  <div class="review-item__layout">
                    <div class="review-avatar">
                      {% if review.user_avatar %}
                        <img src="{{ review.user_avatar }}" alt="{{ review.user_name }}" class="review-avatar__image" />
                      {% else %}
                        <div class="review-avatar__fallback">
                          {{ review.user_name|split(' ')|map(s => s[0])|join|upper }}
                        </div>
                      {% endif %}
                    </div>
                    <div class="review-item__content">
                      <div class="review-item__header">
                        <div class="review-item__user">
                          <p class="review-item__name">{{ review.user_name }}</p>
                          <div class="review-item__meta">
                            <div class="rating-stars rating-stars--small">
                              {% set fullStars = review.rating|floor %}
                              {% set halfStar = (review.rating - fullStars) >= 0.25 and (review.rating - fullStars) < 0.75 ? 1 : 0 %}
                              {% set emptyStars = 5 - fullStars - halfStar %}
                              {% for i in 1..fullStars %}
                                <svg class="rating-stars__star rating-stars__star--filled" viewBox="0 0 24 24">
                                  <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                                </svg>
                              {% endfor %}
                              {% if halfStar %}
                                <svg class="rating-stars__star rating-stars__star--partial" viewBox="0 0 24 24">
                                  <defs>
                                    <linearGradient id="half-grad-small">
                                      <stop offset="50%" stop-color="#FFD700"/>
                                      <stop offset="50%" stop-color="#E0E0E0"/>
                                    </linearGradient>
                                  </defs>
                                  <polygon fill="url(#half-grad-small)" points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                                </svg>
                              {% endif %}
                              {% for i in 1..emptyStars %}
                                <svg class="rating-stars__star rating-stars__star--empty" viewBox="0 0 24 24">
                                  <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                                </svg>
                              {% endfor %}
                            </div>
                            <span class="review-item__date">{{ review.created_at|ago }}</span>
                          </div>
                        </div>
                      </div>
                      <p class="review-item__text">{{ review.comment }}</p>
                      {% if review.images is defined and review.images|length > 0 %}
                        <div class="review-images">
                          {% for img in review.images %}
                            <img src="{{ img }}" alt="Review image" class="review-images__item" />
                          {% endfor %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
                <div class="customer-reviews__separator"></div>
              {% endfor %}
            </div>
          </div>
          {% endif %} #}
        </div>
      </div>
    </div>
  </div>
</div>