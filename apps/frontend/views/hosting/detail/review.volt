<div class="col l-12 mc-12 c-12">
  <div class="customer-reviews">
    <div class="customer-reviews__container">
      <div class="customer-reviews__card">
        <div class="customer-reviews__header box-home__title">
          <h2><PERSON><PERSON><PERSON> g<PERSON> từ khách hàng</h2>
        </div>

        <div class="customer-reviews__content">
          <div class="rating-overview">
            <div class="rating-overview__main">
              <div class="rating-overview__score">
                {{ hosting.rating.overall_avg ? hosting.rating.overall_avg : 0}}
              </div>
              <div class="rating-overview__details">
                <div class="rating-stars">
                  {% set fullStars = hosting.rating.overall_avg ? floor(hosting.rating.overall_avg) : 0 %}
                  {% set halfStar = (hosting.rating.overall_avg and (hosting.rating.overall_avg - fullStars) >= 0.25 and (hosting.rating.overall_avg - fullStars) < 0.75) ? 1 : 0 %}
                  {% set emptyStars = 5 - fullStars - halfStar %}
                  {% for i in 1..fullStars %}
                    <svg class="rating-stars__star rating-stars__star--filled" viewBox="0 0 24 24">
                      <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                    </svg>
                  {% endfor %}
                  {% if halfStar %}
                    <svg class="rating-stars__star rating-stars__star--partial" viewBox="0 0 24 24">
                      <defs>
                        <linearGradient id="half-grad">
                          <stop offset="50%" stop-color="#FFD700"/>
                          <stop offset="50%" stop-color="#E0E0E0"/>
                        </linearGradient>
                      </defs>
                      <polygon fill="url(#half-grad)" points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                    </svg>
                  {% endif %}
                  {% for i in 1..emptyStars %}
                    <svg class="rating-stars__star rating-stars__star--empty" viewBox="0 0 24 24">
                      <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                    </svg>
                  {% endfor %}
                </div>
                <p class="rating-overview__count">
                  Dựa trên {{ hosting.rating.total_reviews ? hosting.rating.total_reviews : 0 }} đánh giá
                </p>
              </div>
            </div>
            <div class="rating-overview__summary">
              <p class="rating-overview__label">
                {{ hosting.rating.overall_avg >= 4.5 ? 'Xuất sắc' : (hosting.rating.overall_avg >= 3.5 ? 'Tốt' : (hosting.rating.overall_avg > 0 ? 'Trung bình' : 'Chưa có đánh giá')) }}
              </p>
              <p class="rating-overview__description">Đánh giá tổng thể</p>
            </div>
          </div>

          <div class="customer-reviews__separator"></div>

          <!-- Metrics Section -->
          <div class="rating-breakdown">
            <h3 class="rating-breakdown__title">Chi tiết đánh giá</h3>
            <div class="rating-breakdown__list">
              <!-- Sạch sẽ -->
              <div class="rating-metric">
                <div class="rating-metric__label">Sạch sẽ</div>
                <div class="rating-metric__progress">
                  <div class="rating-metric__bar">
                    <div class="rating-metric__fill" style="width: {{ Ohi.calcPercent(hosting.rating ? hosting.rating.clean_avg : 0, 5) }}%"></div>
                  </div>
                </div>
                <div class="rating-metric__score">
                  <svg class="rating-metric__star" viewBox="0 0 24 24">
                    <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                  </svg>
                  <span class="rating-metric__value">
                    {{ hosting.rating ? hosting.rating.clean_avg : 0 }}
                  </span>
                </div>
              </div>

              <!-- Nhân viên -->
              <div class="rating-metric">
                <div class="rating-metric__label">Nhân viên</div>
                <div class="rating-metric__progress">
                  <div class="rating-metric__bar">
                    <div class="rating-metric__fill" style="width: {{ Ohi.calcPercent(hosting.rating ? hosting.rating.staff_avg : 0, 5) }}%"></div>
                  </div>
                </div>
                <div class="rating-metric__score">
                  <svg class="rating-metric__star" viewBox="0 0 24 24">
                    <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                  </svg>
                  <span class="rating-metric__value">
                    {{ hosting.rating ? hosting.rating.staff_avg : 0 }}
                  </span>
                </div>
              </div>

              <!-- Giá trị -->
              <div class="rating-metric">
                <div class="rating-metric__label">Giá trị</div>
                <div class="rating-metric__progress">
                  <div class="rating-metric__bar">
                    <div class="rating-metric__fill" style="width: {{ Ohi.calcPercent(hosting.rating ? hosting.rating.value_avg : 0, 5) }}%"></div>
                  </div>
                </div>
                <div class="rating-metric__score">
                  <svg class="rating-metric__star" viewBox="0 0 24 24">
                    <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                  </svg>
                  <span class="rating-metric__value">
                    {{ hosting.rating ? hosting.rating.value_avg : 0 }}
                  </span>
                </div>
              </div>

              <!-- Vị trí -->
              <div class="rating-metric">
                <div class="rating-metric__label">Vị trí</div>
                <div class="rating-metric__progress">
                  <div class="rating-metric__bar">
                    <div class="rating-metric__fill" style="width: {{ Ohi.calcPercent(hosting.rating ? hosting.rating.location_avg : 0, 5) }}%"></div>
                  </div>
                </div>
                <div class="rating-metric__score">
                  <svg class="rating-metric__star" viewBox="0 0 24 24">
                    <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                  </svg>
                  <span class="rating-metric__value">
                    {{ hosting.rating ? hosting.rating.location_avg : 0 }}
                  </span>
                </div>
              </div>

              <!-- Tiện nghi -->
              <div class="rating-metric">
                <div class="rating-metric__label">Tiện nghi</div>
                <div class="rating-metric__progress">
                  <div class="rating-metric__bar">
                    <div class="rating-metric__fill" style="width: {{ Ohi.calcPercent(hosting.rating ? hosting.rating.amenities_avg : 0, 5) }}%"></div>
                  </div>
                </div>
                <div class="rating-metric__score">
                  <svg class="rating-metric__star" viewBox="0 0 24 24">
                    <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                  </svg>
                  <span class="rating-metric__value">
                    {{ hosting.rating ? hosting.rating.amenities_avg : 0 }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="customer-reviews__separator"></div>

          <!-- Individual Reviews Section -->
          {% if hosting.getReviews() is not null and hosting.getReviews()|length > 0 %}
          <div class="reviews-section">
            <h3 class="reviews-section__title">Đánh giá gần đây</h3>
            <div class="reviews-list">
              {% for review in hosting.getReviews() %}
                <div class="review-item">
                  <div class="review-item__layout">
                    <div class="review-avatar">
                      {% if review.booking.user is not empty and review.booking.user.avatar is not empty %}
                        <img src="{{ review.booking.user.avatar }}" alt="{{ review.booking.user.fullname }}" class="review-avatar__image" />
                      {% else %}
                        <div class="review-avatar__fallback">
                        {{ review.getAvatarWithName() }} 
                        </div>
                      {% endif %}
                    </div>
                    <div class="review-item__content">
                      <div class="review-item__header">
                        <div class="review-item__user">
                          <p class="review-item__name">{{ review.booking.user_fullname }}</p>
                          <div class="review-item__meta">
                            <div class="rating-stars rating-stars--small">
                              {% set fullStars = review.overall ? floor(review.overall) : 0 %}
                              {% set halfStar = (review.overall - fullStars) >= 0.25 and (review.overall - fullStars) < 0.75 ? 1 : 0 %}
                              {% set emptyStars = 5 - fullStars - halfStar %}
                              {% for i in 1..fullStars %}
                                <svg class="rating-stars__star rating-stars__star--filled" viewBox="0 0 24 24">
                                  <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                                </svg>
                              {% endfor %}
                              {% if halfStar %}
                                <svg class="rating-stars__star rating-stars__star--partial" viewBox="0 0 24 24">
                                  <defs>
                                    <linearGradient id="half-grad-small">
                                      <stop offset="50%" stop-color="#FFD700"/>
                                      <stop offset="50%" stop-color="#E0E0E0"/>
                                    </linearGradient>
                                  </defs>
                                  <polygon fill="url(#half-grad-small)" points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                                </svg>
                              {% endif %}
                              {% for i in 1..emptyStars %}
                                <svg class="rating-stars__star rating-stars__star--empty" viewBox="0 0 24 24">
                                  <polygon points="12,2 15.3,8.2 22,9.3 17,14.2 18.2,21 12,17.8 5.8,21 7,14.2 2,9.3 8.7,8.2"/>
                                </svg>
                              {% endfor %}
                            </div>
                            <span class="review-item__date">{{ date('d/m/Y', strtotime(review.created)) }}</span>
                          </div>
                        </div>
                      </div>
                      <p class="review-item__text">{{ review.content }}</p>
                      {% if review.imgs is defined and review.imgs|length > 0 %}
                        <div class="review-images">
                          {% for img in review.imgs %}
                            <img src="{{ img }}" alt="Review image" class="review-images__item" />
                          {% endfor %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
                <div class="customer-reviews__separator"></div>
              {% endfor %}
            </div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>